// Core Interface
export { type IArtifactHandler } from './interface';

// AI Agent Artifacts
export { AIAgentArtifactUI } from './ai-agent-server-artifact/ai-agent-artifact-ui';

// Database Record Artifacts
export { RecordArtifact, RecordDetailComponent } from './database-record-server-artifact/record-artifact';

// File Artifacts
export { FileArtifact } from './file-artifact/file-artifact';

// HTML Artifacts
export { HtmlArtifact } from './html-server-artifact/html-artifact';

// Node Resource Artifacts
export { NodeResourcesArtifact } from './node-resources-server-artifact/node-resources-artifact';

// Slides Artifacts
export { SlidesArtifact } from './slides-server-artifact/slides-artifact/index';

// Text/Markdown Artifacts
export { MarkdownArtifact } from './text-server-artifact/markdown-artifact';

// Server Artifact UI (main artifact renderer)
export { AIArtifactServer as AIServerArtifact } from './ai-server-artifact-ui';

// Grid and Search Artifacts (from ai/client/chat/artifacts)
export { GridArtifact } from '../ai/client/chat/artifacts/grid-artifact';
export { SearchPageArtifact } from '../ai/client/chat/artifacts/search-page-artifact';
