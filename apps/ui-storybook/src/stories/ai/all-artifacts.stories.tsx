import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import type { ToolInvocation } from '@ai-sdk/ui-utils';
import {
  FileArtifact,
  HtmlArtifact,
  MarkdownArtifact,
  RecordArtifact,
  NodeResourcesArtifact,
  SlidesArtifact
} from '@bika/domains/ai-artifacts';
import type { SkillsetSelectDTO } from '@bika/types/skill/dto';
import type { NodeResource } from '@bika/types/node/bo';
import { FieldVO, RecordVO } from '@bika/types/database/vo';
import { Slides, SlidesOutline } from '@bika/types/ai/vo';
import { FC } from 'react';
import { Box } from '@bika/ui/layouts';
import { Typography } from '@bika/ui/text-components';

// Mock data
const mockTool: ToolInvocation = {
  toolCallId: 'test-tool-call',
  toolName: 'test-tool',
  args: { test: 'args' },
  state: 'result',
  result: { test: 'result' }
};

const mockSkillsets: SkillsetSelectDTO[] = [
  { kind: 'preset', key: 'default' },
  { kind: 'preset', key: 'bika-database' },
  { kind: 'preset', key: 'bika-automation' }
];

const mockNodeResources: NodeResource[] = [
  {
    resourceType: 'DATABASE',
    id: 'node-1',
    name: 'Test Database',
    description: 'A test database resource',
    databaseType: 'DATUM',
    fields: [
      {
        name: 'Title',
        type: 'SINGLE_TEXT',
        id: 'field-1'
      }
    ]
  }
];

const mockRecord: RecordVO = {
  id: 'record-1',
  databaseId: 'database-1',
  cells: {
    'field-1': { id: 'cell-1', value: 'Test Value 1' },
    'field-2': { id: 'cell-2', value: 'Test Value 2' }
  }
};

const mockFields: FieldVO[] = [
  {
    id: 'field-1',
    databaseId: 'database-1',
    name: 'Test Field 1',
    type: 'SINGLE_TEXT',
    property: {},
    primary: true,
    description: 'A test field'
  },
  {
    id: 'field-2',
    databaseId: 'database-1',
    name: 'Test Field 2',
    type: 'SINGLE_TEXT',
    property: {},
    primary: false,
    description: 'Another test field'
  }
];

const mockSlides: Slides = [
  {
    slide_number: 1,
    title: 'Introduction',
    type: 'title',
    html_content: '<h1>Welcome to Our Presentation</h1><p>This is the introduction slide.</p>'
  },
  {
    slide_number: 2,
    title: 'Main Content',
    type: 'content',
    html_content: '<h2>Main Content</h2><ul><li>Point 1</li><li>Point 2</li><li>Point 3</li></ul>'
  }
];

const mockSlidesOutline: SlidesOutline = {
  userPrompt: 'Create a test presentation',
  outline: {
    title: 'Test Presentation',
    slides: [
      {
        slide_number: 1,
        slide_title: 'Introduction',
        slide_type: 'title',
        description: 'Welcome slide',
        slide_data: {
          subtitle: 'Welcome to our presentation',
          author: 'Test Author',
          date: new Date().toLocaleDateString()
        }
      },
      {
        slide_number: 2,
        slide_title: 'Main Content',
        slide_type: 'content',
        description: 'Main points',
        slide_data: {
          sections: [
            { heading: 'Point 1', text: 'First main point' },
            { heading: 'Point 2', text: 'Second main point' },
            { heading: 'Point 3', text: 'Third main point' }
          ]
        }
      }
    ]
  },
  slide_template: {
    theme: {
      color_primary: '#3498db',
      color_secondary: '#2c3e50',
      color_text: '#333333',
      color_accent: '#e74c3c',
      color_background: '#ffffff',
      color_surface: '#f8f9fa'
    },
    template: 'Basic slide template'
  }
};



// Story Components
const AllArtifactsStory: FC = () => {
  return (
    <Box sx={{ padding: 2, display: 'flex', flexDirection: 'column', gap: 4 }}>
      <Typography level="h1">All Artifact Components</Typography>

      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>File Artifact</Typography>
        <Box sx={{ height: '400px', border: '1px solid #ccc', borderRadius: 1 }}>
          <FileArtifact
            filePath="/test/file.txt"
            content="This is test file content"
            skillsets={mockSkillsets}
            tool={mockTool}
          />
        </Box>
      </Box>

      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>HTML Artifact</Typography>
        <Box sx={{ height: '400px', border: '1px solid #ccc', borderRadius: 1 }}>
          <HtmlArtifact
            content="<h1>Test HTML</h1><p>This is test HTML content</p>"
            skillsets={mockSkillsets}
            tool={mockTool}
            data={{ html: "<h1>Test HTML</h1><p>This is test HTML content</p>" }}
          />
        </Box>
      </Box>

      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>Markdown Artifact</Typography>
        <Box sx={{ height: '400px', border: '1px solid #ccc', borderRadius: 1 }}>
          <MarkdownArtifact
            skillsets={mockSkillsets}
            tool={mockTool}
            value={{
              content: "# Test Markdown\n\nThis is **test** markdown content with:\n\n- Item 1\n- Item 2\n- Item 3",
              title: "Test Document"
            }}
          />
        </Box>
      </Box>

      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>Record Artifact</Typography>
        <Box sx={{ height: '400px', border: '1px solid #ccc', borderRadius: 1 }}>
          <RecordArtifact
            data={[mockRecord]}
            fields={mockFields}
            skillsets={mockSkillsets}
            tool={mockTool}
          />
        </Box>
      </Box>

      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>Node Resources Artifact</Typography>
        <Box sx={{ height: '400px', border: '1px solid #ccc', borderRadius: 1 }}>
          <NodeResourcesArtifact
            resources={mockNodeResources}
            skillsets={mockSkillsets}
            tool={mockTool}
          />
        </Box>
      </Box>

      <Box>
        <Typography level="h2" sx={{ mb: 2 }}>Slides Artifact</Typography>
        <Box sx={{ height: '600px', border: '1px solid #ccc', borderRadius: 1 }}>
          <SlidesArtifact
            slides={mockSlides}
            outline={mockSlidesOutline}
          />
        </Box>
      </Box>


    </Box>
  );
};

// Storybook configuration
export default {
  title: '@bika/ai/All Artifacts',
  component: AllArtifactsStory,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A comprehensive showcase of all artifact components that use ArtifactContainer'
      }
    }
  },
  tags: ['autodocs'],
} satisfies Meta<typeof AllArtifactsStory>;

type Story = StoryObj<typeof AllArtifactsStory>;

export const AllArtifacts: Story = {
  name: 'All Artifact Components',
  parameters: {
    docs: {
      description: {
        story: 'Displays all artifact components that use ArtifactContainer in a single view for easy comparison and testing.'
      }
    }
  }
};

// Individual artifact stories for focused testing
export const FileArtifactOnly: Story = {
  render: () => (
    <Box sx={{ padding: 2, height: '500px' }}>
      <FileArtifact
        filePath="/test/document.pdf"
        content="Sample file content"
        skillsets={mockSkillsets}
        tool={mockTool}
      />
    </Box>
  )
};

export const HtmlArtifactOnly: Story = {
  render: () => (
    <Box sx={{ padding: 2, height: '500px' }}>
      <HtmlArtifact
        content="<h1>Sample HTML</h1><p>This is a sample HTML artifact</p><ul><li>Item 1</li><li>Item 2</li></ul>"
        skillsets={mockSkillsets}
        tool={mockTool}
        data={{ html: "<h1>Sample HTML</h1><p>This is a sample HTML artifact</p><ul><li>Item 1</li><li>Item 2</li></ul>" }}
      />
    </Box>
  )
};

export const MarkdownArtifactOnly: Story = {
  render: () => (
    <Box sx={{ padding: 2, height: '500px' }}>
      <MarkdownArtifact
        skillsets={mockSkillsets}
        tool={mockTool}
        value={{
          content: "# Sample Document\n\nThis is a **sample** markdown document with:\n\n## Features\n\n- **Bold text**\n- *Italic text*\n- `Code snippets`\n- [Links](https://example.com)\n\n## Code Block\n\n```javascript\nfunction hello() {\n  console.log('Hello, World!');\n}\n```",
          title: "Sample Markdown Document"
        }}
      />
    </Box>
  )
};

export const SlidesArtifactOnly: Story = {
  render: () => (
    <Box sx={{ padding: 2, height: '600px' }}>
      <SlidesArtifact
        slides={mockSlides}
        outline={mockSlidesOutline}
      />
    </Box>
  )
};

